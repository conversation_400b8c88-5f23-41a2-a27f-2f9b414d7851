<?php
/**
 * Schedule Model
 * Vietnamese Classroom Management System
 */

require_once __DIR__ . '/../config/database.php';

class Schedule {
    private $conn;
    private $table = 'schedules';

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Get current week in YYYY-WXX format
     */
    public static function getCurrentWeek() {
        $date = new DateTime();
        $year = $date->format('Y');
        $week = $date->format('W');
        return "{$year}-W{$week}";
    }

    /**
     * Get next week in YYYY-WXX format
     */
    public static function getNextWeek() {
        $date = new DateTime();
        $date->add(new DateInterval('P7D')); // Add 7 days
        $year = $date->format('Y');
        $week = $date->format('W');
        return "{$year}-W{$week}";
    }

    /**
     * Get week from date
     */
    public static function getWeekFromDate($date) {
        $dateObj = new DateTime($date);
        $year = $dateObj->format('Y');
        $week = $dateObj->format('W');
        return "{$year}-W{$week}";
    }

    /**
     * Create new schedule
     */
    public function create($data) {
        $sql = "INSERT INTO {$this->table} 
                (class_id, teacher_id, day_of_week, session, start_time, end_time, 
                 subject, room, week_number, week_created, is_active, created_at) 
                VALUES (:class_id, :teacher_id, :day_of_week, :session, :start_time, :end_time,
                        :subject, :room, :week_number, :week_created, :is_active, NOW())";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':class_id', $data['class_id']);
            $stmt->bindParam(':teacher_id', $data['teacher_id']);
            $stmt->bindParam(':day_of_week', $data['day_of_week']);
            $stmt->bindParam(':session', $data['session']);
            $stmt->bindParam(':start_time', $data['start_time']);
            $stmt->bindParam(':end_time', $data['end_time']);
            $stmt->bindParam(':subject', $data['subject']);
            $stmt->bindParam(':room', $data['room']);
            $stmt->bindParam(':week_number', $data['week_number']);
            $stmt->bindParam(':week_created', $data['week_created'] ?? self::getCurrentWeek());
            $stmt->bindParam(':is_active', $data['is_active'] ?? 1);
            
            if ($stmt->execute()) {
                return $this->conn->lastInsertId();
            }
            return false;
        } catch (PDOException $e) {
            error_log("Schedule creation error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get schedule by ID
     */
    public function getById($id) {
        $sql = "SELECT s.*, c.name as class_name, u.full_name as teacher_name 
                FROM {$this->table} s 
                LEFT JOIN classes c ON s.class_id = c.id 
                LEFT JOIN users u ON s.teacher_id = u.id 
                WHERE s.id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get schedule error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get schedules by week
     */
    public function getByWeek($week_number, $class_id = null, $teacher_id = null) {
        $sql = "SELECT s.*, c.name as class_name, u.full_name as teacher_name 
                FROM {$this->table} s 
                LEFT JOIN classes c ON s.class_id = c.id 
                LEFT JOIN users u ON s.teacher_id = u.id 
                WHERE s.week_number = :week_number AND s.is_active = 1";
        
        if ($class_id) {
            $sql .= " AND s.class_id = :class_id";
        }
        if ($teacher_id) {
            $sql .= " AND s.teacher_id = :teacher_id";
        }
        
        $sql .= " ORDER BY s.day_of_week, s.start_time";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':week_number', $week_number);
            if ($class_id) {
                $stmt->bindParam(':class_id', $class_id);
            }
            if ($teacher_id) {
                $stmt->bindParam(':teacher_id', $teacher_id);
            }
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get schedules by week error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get all schedules
     */
    public function getAll($active_only = true) {
        $sql = "SELECT s.*, c.name as class_name, u.full_name as teacher_name 
                FROM {$this->table} s 
                LEFT JOIN classes c ON s.class_id = c.id 
                LEFT JOIN users u ON s.teacher_id = u.id";
        
        if ($active_only) {
            $sql .= " WHERE s.is_active = 1";
        }
        
        $sql .= " ORDER BY s.week_number DESC, s.day_of_week, s.start_time";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get all schedules error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Update schedule
     */
    public function update($id, $data) {
        $sql = "UPDATE {$this->table} 
                SET class_id = :class_id, teacher_id = :teacher_id, day_of_week = :day_of_week,
                    session = :session, start_time = :start_time, end_time = :end_time,
                    subject = :subject, room = :room, week_number = :week_number,
                    is_active = :is_active, updated_at = NOW()
                WHERE id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            $stmt->bindParam(':class_id', $data['class_id']);
            $stmt->bindParam(':teacher_id', $data['teacher_id']);
            $stmt->bindParam(':day_of_week', $data['day_of_week']);
            $stmt->bindParam(':session', $data['session']);
            $stmt->bindParam(':start_time', $data['start_time']);
            $stmt->bindParam(':end_time', $data['end_time']);
            $stmt->bindParam(':subject', $data['subject']);
            $stmt->bindParam(':room', $data['room']);
            $stmt->bindParam(':week_number', $data['week_number']);
            $stmt->bindParam(':is_active', $data['is_active'] ?? 1);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Schedule update error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete schedule (soft delete)
     */
    public function delete($id) {
        $sql = "UPDATE {$this->table} SET is_active = 0, updated_at = NOW() WHERE id = :id";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':id', $id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Schedule delete error: " . $e->getMessage());
            return false;
        }
    }





    /**
     * Get day name in Vietnamese
     */
    public static function getDayName($day_of_week) {
        $days = [
            1 => 'Thứ 2',
            2 => 'Thứ 3',
            3 => 'Thứ 4',
            4 => 'Thứ 5',
            5 => 'Thứ 6',
            6 => 'Thứ 7',
            7 => 'Chủ nhật'
        ];
        return $days[$day_of_week] ?? '';
    }

    /**
     * Get session name in Vietnamese
     */
    public static function getSessionName($session) {
        return $session === 'morning' ? 'Buổi sáng' : 'Buổi chiều';
    }

    /**
     * Check for schedule conflicts
     */
    public function hasConflict($data, $exclude_id = null) {
        $sql = "SELECT COUNT(*) FROM {$this->table} 
                WHERE teacher_id = :teacher_id 
                AND day_of_week = :day_of_week 
                AND week_number = :week_number
                AND is_active = 1
                AND (
                    (start_time <= :start_time AND end_time > :start_time) OR
                    (start_time < :end_time AND end_time >= :end_time) OR
                    (start_time >= :start_time AND end_time <= :end_time)
                )";
        
        if ($exclude_id) {
            $sql .= " AND id != :exclude_id";
        }
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':teacher_id', $data['teacher_id']);
            $stmt->bindParam(':day_of_week', $data['day_of_week']);
            $stmt->bindParam(':week_number', $data['week_number']);
            $stmt->bindParam(':start_time', $data['start_time']);
            $stmt->bindParam(':end_time', $data['end_time']);
            if ($exclude_id) {
                $stmt->bindParam(':exclude_id', $exclude_id);
            }
            $stmt->execute();
            return $stmt->fetchColumn() > 0;
        } catch (PDOException $e) {
            error_log("Check schedule conflict error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get schedules by teacher
     */
    public function getByTeacher($teacher_id, $week_number = null) {
        $sql = "SELECT s.*, c.name as class_name 
                FROM {$this->table} s 
                LEFT JOIN classes c ON s.class_id = c.id 
                WHERE s.teacher_id = :teacher_id AND s.is_active = 1";
        
        if ($week_number) {
            $sql .= " AND s.week_number = :week_number";
        }
        
        $sql .= " ORDER BY s.week_number DESC, s.day_of_week, s.start_time";
        
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':teacher_id', $teacher_id);
            if ($week_number) {
                $stmt->bindParam(':week_number', $week_number);
            }
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get schedules by teacher error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get total count of schedules
     */
    public function getTotalCount($active_only = true) {
        $sql = "SELECT COUNT(*) FROM {$this->table}";

        if ($active_only) {
            $sql .= " WHERE is_active = 1";
        }

        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            return $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Get total schedules count error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get teacher classes
     */
    public function getTeacherClasses($teacher_id) {
        $sql = "SELECT DISTINCT c.id, c.name
                FROM {$this->table} s
                JOIN classes c ON s.class_id = c.id
                WHERE s.teacher_id = :teacher_id AND s.is_active = 1 AND c.is_active = 1
                ORDER BY c.name";

        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':teacher_id', $teacher_id);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get teacher classes error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get available weeks
     */
    public function getAvailableWeeks() {
        $sql = "SELECT DISTINCT week_number
                FROM {$this->table}
                WHERE is_active = 1
                ORDER BY week_number DESC";

        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (PDOException $e) {
            error_log("Get available weeks error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Copy week schedules
     */
    public function copyWeekSchedules($from_week, $to_week) {
        try {
            // Get schedules from source week
            $sql = "SELECT class_id, teacher_id, day_of_week, session, start_time, end_time, subject, room
                    FROM {$this->table}
                    WHERE week_number = :from_week AND is_active = 1";

            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':from_week', $from_week);
            $stmt->execute();
            $schedules = $stmt->fetchAll();

            $success_count = 0;
            foreach ($schedules as $schedule) {
                $newSchedule = [
                    'class_id' => $schedule['class_id'],
                    'teacher_id' => $schedule['teacher_id'],
                    'day_of_week' => $schedule['day_of_week'],
                    'session' => $schedule['session'],
                    'start_time' => $schedule['start_time'],
                    'end_time' => $schedule['end_time'],
                    'subject' => $schedule['subject'],
                    'room' => $schedule['room'],
                    'week_number' => $to_week,
                    'week_created' => getCurrentWeek(),
                    'is_active' => 1
                ];

                if ($this->create($newSchedule)) {
                    $success_count++;
                }
            }

            return $success_count > 0;
        } catch (PDOException $e) {
            error_log("Copy week schedules error: " . $e->getMessage());
            return false;
        }
    }
}
?>
